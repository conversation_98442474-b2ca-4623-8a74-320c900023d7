package com.jly.sharding.adapter.config;

import com.jly.sharding.adapter.enums.SqlType;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 分片映射配置加载器
 * 
 * <p>加载 sharding-mapper-plugin 生成的配置文件，提供表名和SQL类型查询功能：</p>
 * <ul>
 *   <li><strong>灵活配置</strong>：支持 file:、classpath:、classpath*: 等多种路径格式</li>
 *   <li><strong>线程安全</strong>：使用 ConcurrentHashMap 确保线程安全</li>
 *   <li><strong>缓存机制</strong>：内存缓存提高查询性能</li>
 *   <li><strong>Spring集成</strong>：作为 Spring Bean 可在 XML 中配置</li>
 * </ul>
 * 
 * <p><strong>配置示例：</strong></p>
 * <pre>
 * &lt;bean id="shardingMappingConfigLoader" class="com.jly.sharding.adapter.config.ShardingMappingConfigLoader"&gt;
 *   &lt;property name="mappingsFileLocation"&gt;
 *     &lt;value&gt;classpath:sharding-mappings.properties&lt;/value&gt;
 *   &lt;/property&gt;
 *   &lt;property name="tablesFileLocation"&gt;
 *     &lt;value&gt;classpath:sharding-tables&lt;/value&gt;
 *   &lt;/property&gt;
 * &lt;/bean&gt;
 * </pre>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-17
 */
@Slf4j
public class ShardingMappingConfigLoader implements InitializingBean {

    /**
     * 默认映射文件位置（插件生成的标准文件）
     */
    private static final String DEFAULT_MAPPINGS_FILE = "classpath:sharding-mappings.properties";

    /**
     * 默认表名文件位置（插件生成的标准文件）
     */
    private static final String DEFAULT_TABLES_FILE = "classpath:sharding-tables";

    /**
     * 映射文件地址配置，支持多个文件用逗号分隔
     * 如果不配置，默认加载插件生成的 classpath:sharding-mappings.properties
     * 如果配置了，则加载配置的文件（可以包含默认文件，也可以完全替换）
     *
     * 格式：className.methodName=tableName1,tableName2|SqlType
     * 示例：classpath:sharding-mappings.properties,classpath:custom-mappings.properties
     */
    @Setter
    private String mappingsAddr;

    /**
     * 表名文件地址配置，支持多个文件用逗号分隔
     * 如果不配置，默认加载插件生成的 classpath:sharding-tables
     * 如果配置了，则加载配置的文件（可以包含默认文件，也可以完全替换）
     *
     * 格式：t_user,t_order,t_payment
     * 示例：classpath:sharding-tables,classpath:custom-tables
     */
    @Setter
    private String tablesAddr;

    /**
     * 方法到表名的映射缓存
     * Key: className.methodName
     * Value: Set<String> 表名集合
     */
    private final Map<String, Set<String>> methodToTablesCache = new ConcurrentHashMap<>();

    /**
     * 方法到SQL类型的映射缓存
     * Key: className.methodName
     * Value: SqlType
     */
    private final Map<String, SqlType> methodToSqlTypeCache = new ConcurrentHashMap<>();

    /**
     * 所有逻辑表名集合
     */
    private final Set<String> allLogicTables = ConcurrentHashMap.newKeySet();

    /**
     * 初始化标识
     */
    @Getter
    private volatile boolean initialized = false;
    private final Object initLock = new Object();

    /**
     * 统计信息
     */
    private int methodMappingCount = 0;
    private int logicTableCount = 0;
    private int mappingsFileCount = 0;
    private int tablesFileCount = 0;
    private long loadTime = 0;

    @Override
    public void afterPropertiesSet() throws Exception {
        if (!initialized) {
            synchronized (initLock) {
                if (!initialized) {
                    loadShardingMappingConfig();
                    initialized = true;
                    log.info("ShardingMappingConfigLoader初始化完成: 映射文件数={}, 表文件数={}, 方法映射={}, 逻辑表={}, 耗时={}ms",
                            mappingsFileCount, tablesFileCount, methodMappingCount, logicTableCount, loadTime);
                }
            }
        }
    }

    /**
     * 加载分片映射配置
     */
    private void loadShardingMappingConfig() {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始加载分片映射配置...");

            // 1. 加载表名文件
            loadTablesFile();

            // 2. 加载映射文件
            loadMappingsFile();

            log.info("分片映射配置加载完成");

        } catch (Exception e) {
            log.error("加载分片映射配置失败", e);
            throw new RuntimeException("Failed to load sharding mapping config", e);
        } finally {
            loadTime = System.currentTimeMillis() - startTime;
        }
    }

    /**
     * 加载表名文件
     */
    private void loadTablesFile() throws IOException {
        List<String> fileLocations = resolveTablesFileLocations();

        if (fileLocations.isEmpty()) {
            log.warn("表名文件位置为空，跳过加载");
            return;
        }

        log.debug("开始加载表名文件，文件数量: {}", fileLocations.size());

        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

        for (String location : fileLocations) {
            try {
                log.debug("加载表名文件: {}", location);
                Resource[] resources = resolver.getResources(location);

                for (Resource resource : resources) {
                    if (resource.exists()) {
                        loadSingleTablesFile(resource);
                        tablesFileCount++;
                    } else {
                        log.warn("表名文件不存在: {}", resource.getDescription());
                    }
                }
            } catch (Exception e) {
                log.error("加载表名文件失败: {}, 错误: {}", location, e.getMessage(), e);
                // 继续加载其他文件，不中断整个过程
            }
        }

        log.info("表名文件加载完成，共加载 {} 个文件，逻辑表总数: {}", tablesFileCount, logicTableCount);
    }

    /**
     * 加载单个表名文件
     */
    private void loadSingleTablesFile(Resource resource) throws IOException {
        int beforeCount = logicTableCount;

        try (InputStream is = resource.getInputStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(is, "UTF-8"))) {

            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty() && !line.startsWith("#")) {
                    // 表名文件格式：t_user,t_order,t_payment
                    String[] tables = line.split(",");
                    for (String table : tables) {
                        String trimmedTable = table.trim();
                        if (!trimmedTable.isEmpty()) {
                            if (allLogicTables.add(trimmedTable)) {
                                logicTableCount++;
                            }
                        }
                    }
                }
            }
        }

        int addedCount = logicTableCount - beforeCount;
        log.debug("表名文件加载完成: {}, 新增表数量: {}", resource.getDescription(), addedCount);
    }

    /**
     * 加载映射文件
     */
    private void loadMappingsFile() throws IOException {
        List<String> fileLocations = resolveMappingsFileLocations();

        if (fileLocations.isEmpty()) {
            log.warn("映射文件位置为空，跳过加载");
            return;
        }

        log.debug("开始加载映射文件，文件数量: {}", fileLocations.size());

        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

        for (String location : fileLocations) {
            try {
                log.debug("加载映射文件: {}", location);
                Resource[] resources = resolver.getResources(location);

                for (Resource resource : resources) {
                    if (resource.exists()) {
                        loadSingleMappingsFile(resource);
                        mappingsFileCount++;
                    } else {
                        log.warn("映射文件不存在: {}", resource.getDescription());
                    }
                }
            } catch (Exception e) {
                log.error("加载映射文件失败: {}, 错误: {}", location, e.getMessage(), e);
                // 继续加载其他文件，不中断整个过程
            }
        }

        log.info("映射文件加载完成，共加载 {} 个文件，方法映射总数: {}", mappingsFileCount, methodMappingCount);
    }

    /**
     * 加载单个映射文件
     */
    private void loadSingleMappingsFile(Resource resource) throws IOException {
        int beforeCount = methodMappingCount;

        Properties properties = new Properties();
        try (InputStream is = resource.getInputStream()) {
            properties.load(is);
        }

        for (Map.Entry<Object, Object> entry : properties.entrySet()) {
            String methodKey = (String) entry.getKey();
            String mappingValue = (String) entry.getValue();

            // 检查是否已存在，避免重复覆盖
            if (methodToTablesCache.containsKey(methodKey)) {
                log.warn("方法映射已存在，将被覆盖: {} (来源: {})", methodKey, resource.getDescription());
            }

            parseMappingEntry(methodKey, mappingValue);
            methodMappingCount++;
        }

        int addedCount = methodMappingCount - beforeCount;
        log.debug("映射文件加载完成: {}, 新增映射数量: {}", resource.getDescription(), addedCount);
    }

    /**
     * 解析映射条目
     * 
     * @param methodKey 方法键，格式：className.methodName
     * @param mappingValue 映射值，格式：tableName1,tableName2|SqlType
     */
    private void parseMappingEntry(String methodKey, String mappingValue) {
        if (!StringUtils.hasText(methodKey) || !StringUtils.hasText(mappingValue)) {
            return;
        }

        try {
            String tablesPart;
            SqlType sqlType = SqlType.Query; // 默认值

            // 解析新格式：tableName1,tableName2|SqlType
            if (mappingValue.contains("|")) {
                String[] parts = mappingValue.split("\\|");
                tablesPart = parts[0];
                if (parts.length >= 2) {
                    try {
                        sqlType = SqlType.valueOf(parts[1].trim());
                    } catch (IllegalArgumentException e) {
                        log.warn("无效的SQL类型: {}, 方法: {}, 使用默认值Query", parts[1], methodKey);
                    }
                }
            } else {
                // 兼容旧格式：tableName1,tableName2
                tablesPart = mappingValue;
            }

            // 解析表名
            Set<String> tables = new HashSet<>();
            if (StringUtils.hasText(tablesPart)) {
                String[] tableArray = tablesPart.split(",");
                for (String table : tableArray) {
                    String trimmedTable = table.trim();
                    if (!trimmedTable.isEmpty()) {
                        tables.add(trimmedTable);
                    }
                }
            }

            // 缓存结果
            if (!tables.isEmpty()) {
                methodToTablesCache.put(methodKey, tables);
            }
            methodToSqlTypeCache.put(methodKey, sqlType);

            log.debug("解析映射: {} -> 表:{}, SQL类型:{}", methodKey, tables, sqlType);

        } catch (Exception e) {
            log.warn("解析映射条目失败: {} = {}, 错误: {}", methodKey, mappingValue, e.getMessage());
        }
    }

    /**
     * 获取方法对应的逻辑表名集合
     * 
     * @param className 类名
     * @param methodName 方法名
     * @return 逻辑表名集合，如果未找到返回空集合
     */
    public Set<String> getLogicTablesForMethod(String className, String methodName) {
        if (!StringUtils.hasText(className) || !StringUtils.hasText(methodName)) {
            return Collections.emptySet();
        }

        String methodKey = className + "." + methodName;
        Set<String> tables = methodToTablesCache.get(methodKey);
        
        if (tables != null) {
            log.debug("找到方法 {} 对应的逻辑表: {}", methodKey, tables);
            return new HashSet<>(tables); // 返回副本避免外部修改
        }

        log.debug("未找到方法 {} 对应的逻辑表", methodKey);
        return Collections.emptySet();
    }

    /**
     * 获取方法对应的SQL类型
     * 
     * @param className 类名
     * @param methodName 方法名
     * @return SQL类型，如果未找到返回默认值Query
     */
    public SqlType getSqlTypeForMethod(String className, String methodName) {
        if (!StringUtils.hasText(className) || !StringUtils.hasText(methodName)) {
            return SqlType.Query;
        }

        String methodKey = className + "." + methodName;
        SqlType sqlType = methodToSqlTypeCache.get(methodKey);
        
        if (sqlType != null) {
            log.debug("找到方法 {} 对应的SQL类型: {}", methodKey, sqlType);
            return sqlType;
        }

        log.debug("未找到方法 {} 对应的SQL类型，使用默认值Query", methodKey);
        return SqlType.Query;
    }

    /**
     * 获取所有逻辑表名
     * 
     * @return 所有逻辑表名集合
     */
    public Set<String> getAllLogicTables() {
        return new HashSet<>(allLogicTables); // 返回副本避免外部修改
    }

    /**
     * 检查是否包含指定的逻辑表
     * 
     * @param tableName 表名
     * @return 是否包含
     */
    public boolean containsLogicTable(String tableName) {
        return allLogicTables.contains(tableName);
    }

    /**
     * 解析映射文件位置列表
     * 逻辑：如果配置了 mappingsAddr，则使用配置的文件；否则使用默认文件
     *
     * @return 解析后的映射文件位置列表
     */
    private List<String> resolveMappingsFileLocations() {
        List<String> result = new ArrayList<>();

        if (StringUtils.hasText(mappingsAddr)) {
            // 如果配置了 mappingsAddr，使用配置的文件（支持逗号分隔）
            String[] locations = mappingsAddr.split(",");
            for (String location : locations) {
                String trimmed = location.trim();
                if (!trimmed.isEmpty()) {
                    result.add(trimmed);
                }
            }
            log.debug("使用配置的映射文件: {}", result);
        } else {
            // 如果没有配置，使用默认的插件生成文件
            result.add(DEFAULT_MAPPINGS_FILE);
            log.debug("使用默认映射文件: {}", DEFAULT_MAPPINGS_FILE);
        }

        return result;
    }

    /**
     * 解析表名文件位置列表
     * 逻辑：如果配置了 tablesAddr，则使用配置的文件；否则使用默认文件
     *
     * @return 解析后的表名文件位置列表
     */
    private List<String> resolveTablesFileLocations() {
        List<String> result = new ArrayList<>();

        if (StringUtils.hasText(tablesAddr)) {
            // 如果配置了 tablesAddr，使用配置的文件（支持逗号分隔）
            String[] locations = tablesAddr.split(",");
            for (String location : locations) {
                String trimmed = location.trim();
                if (!trimmed.isEmpty()) {
                    result.add(trimmed);
                }
            }
            log.debug("使用配置的表名文件: {}", result);
        } else {
            // 如果没有配置，使用默认的插件生成文件
            result.add(DEFAULT_TABLES_FILE);
            log.debug("使用默认表名文件: {}", DEFAULT_TABLES_FILE);
        }

        return result;
    }

    /**
     * 获取统计信息
     */
    public String getStatistics() {
        return String.format("映射文件数: %d, 表文件数: %d, 方法映射数量: %d, 逻辑表数量: %d, 加载耗时: %dms",
                           mappingsFileCount, tablesFileCount, methodMappingCount, logicTableCount, loadTime);
    }

    /**
     * 获取详细统计信息
     */
    public Map<String, Object> getDetailedStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("mappingsFileCount", mappingsFileCount);
        stats.put("tablesFileCount", tablesFileCount);
        stats.put("methodMappingCount", methodMappingCount);
        stats.put("logicTableCount", logicTableCount);
        stats.put("loadTime", loadTime);
        stats.put("initialized", initialized);
        return stats;
    }
}
